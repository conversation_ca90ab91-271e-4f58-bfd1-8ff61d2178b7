using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.InputSystem;
using UnityEngine.EventSystems;

#if ENABLE_INPUT_SYSTEM
using UnityEngine.InputSystem.UI;
#endif


public class UIInputManager : MonoBehaviour
{
    private LastUIInputActions lastuiInputActions;
    private StateManager stateManager;

    [Tooltip("Assign whatever button you want it to start with selected in here.")]
    public Button FirstSelectedButton;

    [HideInInspector] public GameObject SelectedButton;




    private void OnEnable()
    {
        lastuiInputActions.Enable();
    }

    private void OnDisable()
    {
        lastuiInputActions.Disable();
    }

    public void Awake()
    {
        lastuiInputActions = new LastUIInputActions();
        SelectedButton = FirstSelectedButton.gameObject;
    }

    void Start()
    {
        // Get StateManager after it's had time to initialize
        TryGetStateManager();

        // Ensure EventSystem is active for UI input to work
        EnsureEventSystemActive();

        FirstSelectedButton.Select();

        lastuiInputActions.LastUI.Cancel.performed += ctx => ifCancelPressed();
        lastuiInputActions.LastUI.Cancel.performed += ctx => Debug.Log("Go Back Pressed");
        lastuiInputActions.LastUI.Navigate.performed += ctx => changeSliderValue(ctx.ReadValue<Vector2>());
        lastuiInputActions.LastUI.Submit.performed += ctx => SubmitPerformed();

        Debug.Log("[UIInputManager] Input actions configured. Submit action enabled: " + lastuiInputActions.LastUI.Submit.enabled);
    }

    private void EnsureEventSystemActive()
    {
        var eventSystem = FindObjectOfType<EventSystem>();
        if (eventSystem != null)
        {
            if (!eventSystem.gameObject.activeInHierarchy)
            {
                eventSystem.gameObject.SetActive(true);
                Debug.Log("[UIInputManager] Activated EventSystem for UI input");
            }

            if (!eventSystem.enabled)
            {
                eventSystem.enabled = true;
                Debug.Log("[UIInputManager] Enabled EventSystem component");
            }

            // Also ensure the InputSystemUIInputModule is enabled
#if ENABLE_INPUT_SYSTEM
            var inputModule = eventSystem.GetComponent<InputSystemUIInputModule>();
            if (inputModule != null && !inputModule.enabled)
            {
                inputModule.enabled = true;
                Debug.Log("[UIInputManager] Enabled InputSystemUIInputModule");
            }
#endif
        }
        else
        {
            Debug.LogWarning("[UIInputManager] No EventSystem found in scene");
        }
    }

    private void TryGetStateManager()
    {
        stateManager = SceneServiceLocator.Get<StateManager>();

        if (stateManager == null)
        {
            Debug.LogWarning("[UIInputManager] StateManager not found in scene service locator");
        }
    }

    private void Update()
    {
        SelectedButton = EventSystem.current.currentSelectedGameObject;
    }

    void ifCancelPressed()
    {
        if (stateManager != null && stateManager.ActiveCanvas != null && stateManager.ActiveCanvas.canvasType.canGoPreviousCanvas == true)
        {
            StartCoroutine(stateManager.PlayPreviousCanvasAnimation());
        }
    }

    void changeSliderValue(Vector2 direction)
    {
        // Debug.Log(direction);


        if (SelectedButton.TryGetComponent(out ItemController controller))
        {



            switch (SelectedButton.GetComponent<ItemController>().itemType)
            {

                case ItemController.itemTypes.HorizontalSelector:

                    if (direction.x == -1)
                    {
                        SelectedButton.transform.GetChild(0).GetChild(0).GetComponent<Button>().onClick.Invoke();
                    }

                    if (direction.x == 1)
                    {
                        SelectedButton.transform.GetChild(0).GetChild(1).GetComponent<Button>().onClick.Invoke();
                    }


                    return;


            }
        }


    }

    void SubmitPerformed()
    {
        Debug.Log($"[UIInputManager] Submit performed on: {SelectedButton?.name}");

        if (SelectedButton.TryGetComponent(out ItemController controller))
        {
            Debug.Log($"[UIInputManager] ItemController found, type: {controller.itemType}");

            switch (SelectedButton.GetComponent<ItemController>().itemType)
            {
                case ItemController.itemTypes.Toggle:
                    // Toggle the switch state
                    if (SelectedButton.GetComponentInChildren<Toggle>().isOn == true)
                    {
                        SelectedButton.GetComponentInChildren<Toggle>().isOn = false;
                    }
                    else
                    {
                        SelectedButton.GetComponentInChildren<Toggle>().isOn = true;
                    }
                    return;

                case ItemController.itemTypes.Button:
                    // Invoke button click
                    var button = SelectedButton.GetComponent<Button>();
                    if (button != null)
                    {
                        button.onClick.Invoke();
                    }
                    return;

                case ItemController.itemTypes.Slider:
                    // For sliders, we could implement a reset to default or increment behavior
                    // For now, let's just trigger any attached events
                    var slider = SelectedButton.GetComponentInChildren<Slider>();
                    if (slider != null)
                    {
                        // Trigger the slider's onValueChanged event with current value
                        slider.onValueChanged.Invoke(slider.value);
                    }
                    return;

                case ItemController.itemTypes.HorizontalSelector:
                    // For horizontal selectors, simulate clicking the right arrow (next option)
                    if (SelectedButton.transform.childCount > 0 &&
                        SelectedButton.transform.GetChild(0).childCount > 1)
                    {
                        var rightButton = SelectedButton.transform.GetChild(0).GetChild(1).GetComponent<Button>();
                        if (rightButton != null)
                        {
                            rightButton.onClick.Invoke();
                        }
                    }
                    return;

                default:
                    Debug.LogWarning($"[UIInputManager] Submit action not implemented for item type: {controller.itemType}");
                    return;
            }
        }
        else
        {
            // If no ItemController, try to handle as a regular button
            var button = SelectedButton?.GetComponent<Button>();
            if (button != null)
            {
                button.onClick.Invoke();
            }
        }
    }

    public void SelectObject(Selectable select)
    {
        select.Select();
    }











}
