using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class StateController : MonoBehaviour
{
    [Header("Canvas Type")]
    public CanvasType canvasType;

    [Header("Canvas Settings")]
    //public bool canGoPreviousCanvas;
    public CanvasType previousCanvas;

    [Header("UI Settings")]
    public Button StartSelectable;



    StateManager stateManager;




    private void OnEnable()
    {
        // Try to get StateManager, but don't worry if it's not ready yet
        TryGetStateManager();
    }

    private void Start()
    {
        // Ensure we have StateManager by Start
        if (stateManager == null)
        {
            TryGetStateManager();
        }
    }

    private void TryGetStateManager()
    {
        stateManager = SceneServiceLocator.Get<StateManager>();

        if (stateManager == null)
        {
            Debug.LogWarning("[StateController] StateManager not found in scene service locator");
        }
    }


}
