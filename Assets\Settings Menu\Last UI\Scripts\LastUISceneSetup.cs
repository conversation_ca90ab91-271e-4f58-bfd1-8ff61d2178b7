using UnityEngine;
using UnityEngine.EventSystems;

/// <summary>
/// Automatically sets up the Last UI scene with required services.
/// Ensures SceneServiceLocator and LastUISettingsManager are present.
/// </summary>
[DefaultExecutionOrder(-100)] // Execute before other components
public class LastUISceneSetup : MonoBehaviour
{
    [Header("Configuration")]
    [SerializeField] private bool enableDebugLogging = true;
    [SerializeField] private bool autoCreateMissingServices = true;

    private void Awake()
    {
        if (enableDebugLogging)
            Debug.Log("[LastUISceneSetup] Setting up Last UI scene services");

        SetupSceneServiceLocator();
        SetupSettingsManager();
        SetupEventSystemForLastUI();

        if (enableDebugLogging)
            Debug.Log("[LastUISceneSetup] Scene setup complete");
    }

    private void SetupSceneServiceLocator()
    {
        var existingLocator = FindObjectOfType<SceneServiceLocator>();
        if (existingLocator == null && autoCreateMissingServices)
        {
            var locatorGO = new GameObject("SceneServiceLocator");
            locatorGO.AddComponent<SceneServiceLocator>();

            if (enableDebugLogging)
                Debug.Log("[LastUISceneSetup] Created SceneServiceLocator");
        }
        else if (existingLocator != null && enableDebugLogging)
        {
            Debug.Log("[LastUISceneSetup] SceneServiceLocator already exists");
        }
    }

    private void SetupSettingsManager()
    {
        var existingManager = FindObjectOfType<LastUISettingsManager>();
        if (existingManager == null && autoCreateMissingServices)
        {
            var managerGO = new GameObject("LastUISettingsManager");
            managerGO.AddComponent<LastUISettingsManager>();

            if (enableDebugLogging)
                Debug.Log("[LastUISceneSetup] Created LastUISettingsManager");
        }
        else if (existingManager != null && enableDebugLogging)
        {
            Debug.Log("[LastUISceneSetup] LastUISettingsManager already exists");
        }
    }

    private void SetupEventSystemForLastUI()
    {
        var eventSystem = FindObjectOfType<EventSystem>();
        if (eventSystem != null)
        {
            // Ensure EventSystem is active for Last UI to work
            if (!eventSystem.gameObject.activeInHierarchy)
            {
                eventSystem.gameObject.SetActive(true);
                if (enableDebugLogging)
                    Debug.Log("[LastUISceneSetup] Activated EventSystem");
            }

            if (!eventSystem.enabled)
            {
                eventSystem.enabled = true;
                if (enableDebugLogging)
                    Debug.Log("[LastUISceneSetup] Enabled EventSystem component");
            }

            if (enableDebugLogging)
                Debug.Log("[LastUISceneSetup] EventSystem is ready for Last UI");
        }
        else if (enableDebugLogging)
        {
            Debug.LogWarning("[LastUISceneSetup] No EventSystem found in Last UI scene");
        }
    }

#if UNITY_EDITOR
    [ContextMenu("Setup Scene Services")]
    private void EditorSetupServices()
    {
        SetupSceneServiceLocator();
        SetupSettingsManager();
        SetupEventSystemForLastUI();
        Debug.Log("[LastUISceneSetup] Manual scene setup complete");
    }

    [ContextMenu("Validate Scene Setup")]
    private void ValidateSceneSetup()
    {
        var locator = FindObjectOfType<SceneServiceLocator>();
        var settingsManager = FindObjectOfType<LastUISettingsManager>();
        var stateManager = FindObjectOfType<StateManager>();

        Debug.Log("[LastUISceneSetup] Scene validation:");
        Debug.Log($"  - SceneServiceLocator: {(locator != null ? "✓" : "✗")}");
        Debug.Log($"  - LastUISettingsManager: {(settingsManager != null ? "✓" : "✗")}");
        Debug.Log($"  - StateManager: {(stateManager != null ? "✓" : "✗")}");

        if (locator != null && stateManager != null)
        {
            bool isRegistered = SceneServiceLocator.IsRegistered<StateManager>();
            Debug.Log($"  - StateManager registered: {(isRegistered ? "✓" : "✗")}");
        }
    }
#endif
}
