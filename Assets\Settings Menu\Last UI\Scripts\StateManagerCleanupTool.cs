using UnityEngine;
using UnityEngine.SceneManagement;

/// <summary>
/// Tool to help identify and clean up StateManager duplication issues
/// </summary>
public class StateManagerCleanupTool : MonoBehaviour
{
    [Header("Cleanup Settings")]
    [SerializeField] private bool enableDebugLogging = true;
    [SerializeField] private bool autoCleanupOnStart = true;

    private void Start()
    {
        if (autoCleanupOnStart)
        {
            Invoke(nameof(PerformCleanup), 0.1f); // Small delay to let everything initialize
        }
    }

    [ContextMenu("Perform StateManager Cleanup")]
    public void PerformCleanup()
    {
        if (enableDebugLogging)
            Debug.Log("[StateManagerCleanupTool] Starting StateManager cleanup...");

        var allStateManagers = FindObjectsOfType<StateManager>(true);
        
        if (allStateManagers.Length <= 1)
        {
            if (enableDebugLogging)
                Debug.Log("[StateManagerCleanupTool] No duplicate StateManagers found");
            return;
        }

        if (enableDebugLogging)
            Debug.Log($"[StateManagerCleanupTool] Found {allStateManagers.Length} StateManager instances");

        // Strategy: Keep the StateManager that belongs to the Last UI scene
        StateManager lastUIStateManager = null;
        StateManager otherStateManager = null;

        foreach (var sm in allStateManagers)
        {
            if (enableDebugLogging)
            {
                Debug.Log($"[StateManagerCleanupTool] StateManager found:");
                Debug.Log($"  - GameObject: {sm.gameObject.name}");
                Debug.Log($"  - Scene: {sm.gameObject.scene.name}");
                Debug.Log($"  - FirstCanvas: {(sm.FirstCanvas != null ? sm.FirstCanvas.title : "null")}");
            }

            // Check if this StateManager belongs to Last UI scene
            if (IsLastUIScene(sm.gameObject.scene))
            {
                lastUIStateManager = sm;
            }
            else
            {
                otherStateManager = sm;
            }
        }

        // Cleanup logic
        if (lastUIStateManager != null && otherStateManager != null)
        {
            if (enableDebugLogging)
                Debug.Log($"[StateManagerCleanupTool] Destroying StateManager from non-Last UI scene: {otherStateManager.gameObject.scene.name}");

            DestroyImmediate(otherStateManager.gameObject);
        }
        else if (lastUIStateManager == null && otherStateManager != null)
        {
            if (enableDebugLogging)
                Debug.LogWarning("[StateManagerCleanupTool] No Last UI StateManager found, keeping the other one");
        }
        else
        {
            if (enableDebugLogging)
                Debug.LogWarning("[StateManagerCleanupTool] Unexpected StateManager configuration");
        }

        if (enableDebugLogging)
            Debug.Log("[StateManagerCleanupTool] Cleanup complete");
    }

    private bool IsLastUIScene(Scene scene)
    {
        // Check if this is a Last UI scene by looking for specific indicators
        string sceneName = scene.name.ToLower();
        
        // Common Last UI scene names
        if (sceneName.Contains("settings") || 
            sceneName.Contains("lastui") || 
            sceneName.Contains("async") ||
            sceneName.Contains("demo"))
        {
            return true;
        }

        // Check for Last UI specific components in the scene
        var rootObjects = scene.GetRootGameObjects();
        foreach (var rootObj in rootObjects)
        {
            // Look for Last UI specific components
            if (rootObj.GetComponentInChildren<UIInputManager>() != null)
            {
                return true;
            }
        }

        return false;
    }

    [ContextMenu("List All StateManagers")]
    public void ListAllStateManagers()
    {
        var allStateManagers = FindObjectsOfType<StateManager>(true);
        
        Debug.Log($"=== StateManager Instances ({allStateManagers.Length}) ===");
        
        for (int i = 0; i < allStateManagers.Length; i++)
        {
            var sm = allStateManagers[i];
            Debug.Log($"{i + 1}. {sm.gameObject.name} in scene '{sm.gameObject.scene.name}'");
            Debug.Log($"   - Active: {sm.gameObject.activeInHierarchy}");
            Debug.Log($"   - FirstCanvas: {(sm.FirstCanvas != null ? sm.FirstCanvas.title + " (" + sm.FirstCanvas.name + ")" : "null")}");
            
            // Get States count using reflection
            var statesField = typeof(StateManager).GetField("States", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            
            if (statesField != null)
            {
                var states = statesField.GetValue(sm) as System.Collections.Generic.List<GameObject>;
                Debug.Log($"   - States count: {states?.Count ?? 0}");
            }
        }
    }

    [ContextMenu("Force Remove All StateManagers Except Current Scene")]
    public void ForceRemoveAllExceptCurrentScene()
    {
        var currentScene = gameObject.scene;
        var allStateManagers = FindObjectsOfType<StateManager>(true);
        
        foreach (var sm in allStateManagers)
        {
            if (sm.gameObject.scene != currentScene)
            {
                Debug.Log($"[StateManagerCleanupTool] Removing StateManager from scene: {sm.gameObject.scene.name}");
                DestroyImmediate(sm.gameObject);
            }
        }
    }
}
