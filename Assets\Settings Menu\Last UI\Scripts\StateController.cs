using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class StateController : MonoBehaviour
{
    [Header("Canvas Type")]
    public CanvasType canvasType;

    [Header("Canvas Settings")]
    //public bool canGoPreviousCanvas;
    public CanvasType previousCanvas;

    [<PERSON><PERSON>("UI Settings")]
    public Button StartSelectable;

    [Head<PERSON>("Debug Settings")]
    [SerializeField] private bool enableDebugLogging = true;
    [SerializeField] private bool autoActivateOnStart = true;

    StateManager stateManager;

    private void Awake()
    {
        if (enableDebugLogging)
            Debug.Log($"[StateController] Awake called on {gameObject.name} in scene {gameObject.scene.name}");

        ValidateConfiguration();
    }

    private void OnEnable()
    {
        if (enableDebugLogging)
            Debug.Log($"[StateController] OnEnable called on {gameObject.name}");

        // Try to get StateManager, but don't worry if it's not ready yet
        TryGetStateManager();
    }

    private void Start()
    {
        if (enableDebugLogging)
            Debug.Log($"[StateController] Start called on {gameObject.name}");

        // Ensure we have StateManager by Start
        if (stateManager == null)
        {
            TryGetStateManager();
        }

        if (autoActivateOnStart)
        {
            ActivateCanvasType();
        }
    }

    private void ValidateConfiguration()
    {
        if (canvasType == null)
        {
            Debug.LogError($"[StateController] CanvasType is null on {gameObject.name}! This StateController will not function properly.");
            return;
        }

        if (string.IsNullOrEmpty(canvasType.title))
        {
            Debug.LogWarning($"[StateController] CanvasType '{canvasType.name}' has empty title on {gameObject.name}. This may cause navigation issues.");
        }

        if (StartSelectable == null)
        {
            Debug.LogWarning($"[StateController] StartSelectable is null on {gameObject.name}. Auto-selection may not work properly.");
        }

        if (enableDebugLogging)
        {
            Debug.Log($"[StateController] Configuration validated for {gameObject.name}:");
            Debug.Log($"  - CanvasType: {(canvasType != null ? canvasType.name : "null")}");
            Debug.Log($"  - CanvasType Title: '{(canvasType != null ? canvasType.title : "null")}'");
            Debug.Log($"  - PreviousCanvas: {(previousCanvas != null ? previousCanvas.name : "null")}");
            Debug.Log($"  - StartSelectable: {(StartSelectable != null ? StartSelectable.name : "null")}");
            Debug.Log($"  - CanGoPreviousCanvas: {(canvasType != null ? canvasType.canGoPreviousCanvas : false)}");
        }
    }

    private void ActivateCanvasType()
    {
        if (canvasType == null)
        {
            Debug.LogError($"[StateController] Cannot activate canvas - CanvasType is null on {gameObject.name}");
            return;
        }

        if (stateManager == null)
        {
            if (enableDebugLogging)
                Debug.LogWarning($"[StateController] StateManager not available yet for {gameObject.name}. Will try to activate canvas anyway.");
        }

        if (enableDebugLogging)
            Debug.Log($"[StateController] Activating canvas type '{canvasType.title}' ({canvasType.name}) on {gameObject.name}");

        // Activate this GameObject if it's not already active
        if (!gameObject.activeInHierarchy)
        {
            gameObject.SetActive(true);
            if (enableDebugLogging)
                Debug.Log($"[StateController] Activated GameObject {gameObject.name}");
        }

        // If we have a StateManager, use it to properly switch to this canvas
        if (stateManager != null)
        {
            if (enableDebugLogging)
                Debug.Log($"[StateController] Using StateManager to switch to canvas type: {canvasType.title}");

            stateManager.GoToNextCanvas(canvasType);
        }
        else
        {
            // Fallback: just ensure this canvas is active and select the start button
            if (enableDebugLogging)
                Debug.Log($"[StateController] StateManager not available, using fallback activation for {gameObject.name}");

            EnsureCanvasActive();
            SelectStartButton();
        }
    }

    private void EnsureCanvasActive()
    {
        // Make sure this canvas is active
        if (!gameObject.activeInHierarchy)
        {
            gameObject.SetActive(true);
            if (enableDebugLogging)
                Debug.Log($"[StateController] Fallback: Activated canvas {gameObject.name}");
        }

        // Disable other canvases in the same parent (simple fallback)
        if (transform.parent != null)
        {
            foreach (Transform sibling in transform.parent)
            {
                if (sibling != transform && sibling.GetComponent<StateController>() != null)
                {
                    if (sibling.gameObject.activeInHierarchy)
                    {
                        sibling.gameObject.SetActive(false);
                        if (enableDebugLogging)
                            Debug.Log($"[StateController] Fallback: Deactivated sibling canvas {sibling.name}");
                    }
                }
            }
        }
    }

    private void SelectStartButton()
    {
        if (StartSelectable != null)
        {
            StartSelectable.Select();
            if (enableDebugLogging)
                Debug.Log($"[StateController] Selected start button: {StartSelectable.name}");
        }
        else if (enableDebugLogging)
        {
            Debug.LogWarning($"[StateController] No StartSelectable assigned on {gameObject.name}");
        }
    }

    private void TryGetStateManager()
    {
        stateManager = SceneServiceLocator.Get<StateManager>();

        if (stateManager == null)
        {
            if (enableDebugLogging)
                Debug.LogWarning($"[StateController] StateManager not found in scene service locator for {gameObject.name}");
        }
        else if (enableDebugLogging)
        {
            Debug.Log($"[StateController] StateManager found for {gameObject.name}: {stateManager.gameObject.name}");
        }
    }

    /// <summary>
    /// Public method to manually activate this canvas type
    /// </summary>
    [ContextMenu("Activate Canvas Type")]
    public void ManualActivateCanvasType()
    {
        if (enableDebugLogging)
            Debug.Log($"[StateController] Manual activation requested for {gameObject.name}");

        ActivateCanvasType();
    }

    /// <summary>
    /// Public method to validate and log configuration
    /// </summary>
    [ContextMenu("Validate Configuration")]
    public void ManualValidateConfiguration()
    {
        ValidateConfiguration();
    }
}
