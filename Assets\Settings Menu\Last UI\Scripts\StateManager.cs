using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.SceneManagement;

[DefaultExecutionOrder(-50)] // Execute before other components that depend on StateManager
public class StateManager : MonoBehaviour
{
    // Singleton instance
    private static StateManager _instance;
    public static StateManager Instance
    {
        get
        {
            if (_instance == null)
            {
                _instance = FindObjectOfType<StateManager>();
            }
            return _instance;
        }
    }

    [Tooltip("You need to add all of your states in here.")]
    [Header("List of States")]
    [SerializeField]
    List<GameObject> States = new List<GameObject>();


    [Tooltip("Assign starting state in here.")]
    public CanvasType FirstCanvas;

    [Header("Debug Settings")]
    [SerializeField] private bool enableDebugLogging = true;

    [HideInInspector]
    public Animator CanvasAnimator;


    List<StateController> canvasControllerList;

    [HideInInspector]
    public StateController ActiveCanvas;
    [HideInInspector]
    public StateController PreviousCanvas;

    private InspectManager inspectManager;




    private void Awake()
    {
        Debug.Log($"[StateManager] Awake called on {gameObject.name} in scene {gameObject.scene.name}");

        // Singleton enforcement
        if (_instance != null && _instance != this)
        {
            Debug.LogWarning($"[StateManager] Multiple StateManager instances detected. Destroying duplicate in scene {gameObject.scene.name}");
            Destroy(gameObject);
            return;
        }

        _instance = this;

        // Ensure SceneServiceLocator exists first
        EnsureSceneServiceLocator();

        // Register with scene service locator
        SceneServiceLocator.Register(this);

        InitializeStateManager();





    }

    private void EnsureSceneServiceLocator()
    {
        var locator = FindObjectOfType<SceneServiceLocator>();
        if (locator == null)
        {
            Debug.Log("[StateManager] Creating SceneServiceLocator");
            var locatorGO = new GameObject("SceneServiceLocator");
            locatorGO.AddComponent<SceneServiceLocator>();
        }
    }

    private void InitializeStateManager()
    {
        if (enableDebugLogging)
            Debug.Log($"[StateManager] InitializeStateManager called on {gameObject.name}");

        ValidateConfiguration();

        // Initialize CanvasAnimator if not already assigned
        if (CanvasAnimator == null)
        {
            CanvasAnimator = GetComponent<Animator>();
            if (CanvasAnimator == null)
            {
                if (enableDebugLogging)
                    Debug.LogWarning("[StateManager] No Animator component found. Canvas animations will not work.");
            }
            else if (enableDebugLogging)
            {
                Debug.Log("[StateManager] CanvasAnimator successfully assigned");
            }
        }

        if (enableDebugLogging)
            Debug.Log($"[StateManager] Processing {States.Count} states in States list");

        int activatedCount = 0;
        int nullCount = 0;

        foreach (GameObject states in States)
        {
            if (states != null)
            {
                bool wasActive = states.activeInHierarchy;
                states.SetActive(true);
                activatedCount++;

                if (enableDebugLogging)
                    Debug.Log($"[StateManager] Activated state: {states.name} (was active: {wasActive})");
            }
            else
            {
                nullCount++;
                Debug.LogWarning("[StateManager] Null state found in States list. Please remove null entries from the States list in the inspector.");
            }
        }

        if (enableDebugLogging)
            Debug.Log($"[StateManager] States processing complete: {activatedCount} activated, {nullCount} null entries");

        inspectManager = FindObjectOfType<InspectManager>();
        if (enableDebugLogging)
            Debug.Log($"[StateManager] InspectManager found: {(inspectManager != null ? inspectManager.name : "null")}");

        canvasControllerList = GetComponentsInChildren<StateController>().ToList();
        if (enableDebugLogging)
            Debug.Log($"[StateManager] Found {canvasControllerList.Count} StateController components");

        // Log each StateController found
        for (int i = 0; i < canvasControllerList.Count; i++)
        {
            var controller = canvasControllerList[i];
            if (enableDebugLogging)
            {
                Debug.Log($"[StateManager] StateController {i + 1}: {controller.gameObject.name}");
                Debug.Log($"  - CanvasType: {(controller.canvasType != null ? controller.canvasType.name : "null")}");
                Debug.Log($"  - Title: '{(controller.canvasType != null ? controller.canvasType.title : "null")}'");
            }
        }

        // Deactivate all canvases before starting
        if (enableDebugLogging)
            Debug.Log("[StateManager] Deactivating all canvases before starting first canvas");

        canvasControllerList.ForEach(x =>
        {
            if (x.gameObject.activeInHierarchy)
            {
                x.gameObject.SetActive(false);
                if (enableDebugLogging)
                    Debug.Log($"[StateManager] Deactivated canvas: {x.gameObject.name}");
            }
        });

        // Start with FirstCanvas
        if (FirstCanvas != null)
        {
            if (enableDebugLogging)
                Debug.Log($"[StateManager] Starting with FirstCanvas: {FirstCanvas.title} ({FirstCanvas.name})");

            StartCoroutine(PlayNextCanvasAnimation(FirstCanvas));
        }
        else
        {
            Debug.LogError("[StateManager] FirstCanvas is null! Cannot start StateManager properly.");
        }
    }

    private void ValidateConfiguration()
    {
        if (enableDebugLogging)
            Debug.Log("[StateManager] Validating configuration...");

        if (States == null || States.Count == 0)
        {
            Debug.LogError("[StateManager] States list is null or empty! StateManager will not function properly.");
            return;
        }

        if (FirstCanvas == null)
        {
            Debug.LogError("[StateManager] FirstCanvas is null! StateManager cannot determine starting canvas.");
            return;
        }

        if (string.IsNullOrEmpty(FirstCanvas.title))
        {
            Debug.LogWarning($"[StateManager] FirstCanvas '{FirstCanvas.name}' has empty title. This may cause navigation issues.");
        }

        if (enableDebugLogging)
        {
            Debug.Log($"[StateManager] Configuration validation complete:");
            Debug.Log($"  - States count: {States.Count}");
            Debug.Log($"  - FirstCanvas: {FirstCanvas.name}");
            Debug.Log($"  - FirstCanvas title: '{FirstCanvas.title}'");
            Debug.Log($"  - FirstCanvas canGoPreviousCanvas: {FirstCanvas.canGoPreviousCanvas}");
        }
    }

    private void OnEnable()
    {
        StartCoroutine(PlayNextCanvasAnimation(FirstCanvas));
    }

    private void OnDestroy()
    {
        // Clear singleton reference if this is the active instance
        if (_instance == this)
        {
            _instance = null;
            Debug.Log($"[StateManager] Instance cleared on destroy in scene {gameObject.scene.name}");
        }
    }




    public void GoToNextCanvas(CanvasType _type)
    {
        if (enableDebugLogging)
            Debug.Log($"[StateManager] GoToNextCanvas called with type: {(_type != null ? _type.title + " (" + _type.name + ")" : "null")}");

        if (_type == null)
        {
            Debug.LogError("[StateManager] Cannot go to next canvas - CanvasType is null!");
            return;
        }

        if (ActiveCanvas != null)
        {
            if (enableDebugLogging)
                Debug.Log($"[StateManager] Deactivating current canvas: {ActiveCanvas.gameObject.name}");

            ActiveCanvas.gameObject.SetActive(false);
        }
        else if (enableDebugLogging)
        {
            Debug.Log("[StateManager] No active canvas to deactivate");
        }

        if (inspectManager != null)
        {
            inspectManager.DeactiveInspector();
        }
        else if (enableDebugLogging)
        {
            Debug.LogWarning("[StateManager] InspectManager is null, cannot deactivate inspector");
        }

        if (canvasControllerList == null || canvasControllerList.Count == 0)
        {
            Debug.LogError("[StateManager] CanvasControllerList is null or empty! Cannot find target canvas.");
            return;
        }

        if (enableDebugLogging)
            Debug.Log($"[StateManager] Searching for canvas with type '{_type.title}' in {canvasControllerList.Count} controllers");

        StateController NextCanvas = canvasControllerList.Find(x => x.canvasType == _type);
        if (NextCanvas != null)
        {
            if (enableDebugLogging)
                Debug.Log($"[StateManager] Found target canvas: {NextCanvas.gameObject.name}");

            PreviousCanvas = ActiveCanvas;
            NextCanvas.gameObject.SetActive(true);
            ActiveCanvas = NextCanvas;

            if (enableDebugLogging)
                Debug.Log($"[StateManager] Activated canvas: {NextCanvas.gameObject.name}");

            // Select the start button
            var startSelectable = NextCanvas.GetComponent<StateController>().StartSelectable;
            if (startSelectable != null)
            {
                startSelectable.Select();
                if (enableDebugLogging)
                    Debug.Log($"[StateManager] Selected start button: {startSelectable.name}");
            }
            else
            {
                Debug.LogWarning($"[StateManager] StartSelectable is null on {NextCanvas.gameObject.name}");
            }
        }
        else
        {
            Debug.LogError($"[StateManager] The next canvas was not found! Looking for CanvasType: '{_type.title}' ({_type.name})");

            if (enableDebugLogging)
            {
                Debug.Log("[StateManager] Available canvas types:");
                for (int i = 0; i < canvasControllerList.Count; i++)
                {
                    var controller = canvasControllerList[i];
                    Debug.Log($"  {i + 1}. {controller.gameObject.name}: {(controller.canvasType != null ? controller.canvasType.title + " (" + controller.canvasType.name + ")" : "null canvasType")}");
                }
            }
        }
    }

    public void GoToPreviousCanvas()
    {



        if (ActiveCanvas != null)
        {
            ActiveCanvas.gameObject.SetActive(false);
        }

        inspectManager.DeactiveInspector();

        StateController NextCanvas = canvasControllerList.Find(x => x.canvasType == ActiveCanvas.previousCanvas);

        Debug.Log(NextCanvas);

        if (ActiveCanvas.canvasType.canGoPreviousCanvas == true)
        {
            PreviousCanvas = ActiveCanvas;
            NextCanvas.gameObject.SetActive(true);
            ActiveCanvas = NextCanvas;
            NextCanvas.GetComponent<StateController>().StartSelectable.Select();

            //Debug.Log("Can go previous canvas.");

        }
        else
        {
            //Debug.Log("Can't go previous canvas.");
        }
        //Debug.Log("Go Back Performed");
    }



    public IEnumerator PlayNextCanvasAnimation(CanvasType _type)
    {
        // Ensure CanvasAnimator is available
        if (CanvasAnimator == null)
        {
            Debug.LogWarning("[StateManager] CanvasAnimator is null. Skipping animation and going directly to canvas.");
            GoToNextCanvas(_type);
            yield break;
        }

        CanvasAnimator.Play("out_canvas");
        yield return new WaitForSeconds(0.1f);
        GoToNextCanvas(_type);

        CanvasAnimator.Play("in_canvas");
    }

    public IEnumerator PlayPreviousCanvasAnimation()
    {
        // Ensure CanvasAnimator is available
        if (CanvasAnimator == null)
        {
            Debug.LogWarning("[StateManager] CanvasAnimator is null. Skipping animation and going directly to previous canvas.");
            GoToPreviousCanvas();
            yield break;
        }

        CanvasAnimator.Play("out_canvas");
        yield return new WaitForSeconds(0.1f);
        GoToPreviousCanvas();
        CanvasAnimator.Play("in_canvas");
    }

    public void LeaveGame()
    {
        Application.Quit();
        Debug.Log("When you build, your game will close when submit this button.");
    }






}
