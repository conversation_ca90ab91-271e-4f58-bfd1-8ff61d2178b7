using UnityEngine;
using UnityEngine.InputSystem;

namespace Stylo.MenUI
{
    /// <summary>
    /// Simple input bridge for Last UI scene to handle exit requests back to MenUI.
    /// Replaces the complex LastUIMenUIBridge with a cleaner, more direct approach.
    /// </summary>
    public class LastUIInputBridge : MonoBehaviour
    {
        [Header("Configuration")]
        [SerializeField] private bool enableDebugLogging = true;
        [SerializeField] private bool exitOnMainMenuOnly = true;

        // Input system
        private DefaultControls _controls;
        private InputAction _cancelAction;

        // Components
        private StateManager _stateManager;
        private LastUISceneBridge _sceneBridge;

        private void Awake()
        {
            if (enableDebugLogging)
                Debug.Log($"[LastUIInputBridge] Awake called on {gameObject.name} in scene {gameObject.scene.name}");

            // Setup input
            _controls = new DefaultControls();
            _cancelAction = _controls.UI.Cancel;

            // Find components
            _stateManager = FindFirstObjectByType<StateManager>();
            if (enableDebugLogging)
                Debug.Log($"[LastUIInputBridge] StateManager found: {(_stateManager != null ? _stateManager.gameObject.name : "null")}");

            _sceneBridge = FindFirstObjectByType<LastUISceneBridge>();
            if (enableDebugLogging)
                Debug.Log($"[LastUIInputBridge] LastUISceneBridge found: {(_sceneBridge != null ? _sceneBridge.gameObject.name : "null")}");

            if (enableDebugLogging)
                Debug.Log("[LastUIInputBridge] Initialized in Last UI scene");
        }

        private void OnEnable()
        {
            if (_controls != null)
            {
                _controls.Enable();
                if (_cancelAction != null)
                    _cancelAction.performed += OnCancelInput;
            }
        }

        private void OnDisable()
        {
            if (_controls != null)
            {
                if (_cancelAction != null)
                    _cancelAction.performed -= OnCancelInput;
                _controls.Disable();
            }
        }

        private void OnDestroy()
        {
            _controls?.Dispose();
        }

        /// <summary>
        /// Initialize the bridge with reference to the scene bridge
        /// </summary>
        public void Initialize(LastUISceneBridge sceneBridge)
        {
            _sceneBridge = sceneBridge;

            if (enableDebugLogging)
                Debug.Log("[LastUIInputBridge] Initialized with scene bridge reference");
        }

        private void OnCancelInput(InputAction.CallbackContext context)
        {
            if (enableDebugLogging)
                Debug.Log("[LastUIInputBridge] Cancel input detected");

            HandleExitRequest();
        }

        private void HandleExitRequest()
        {
            // Check if we should exit based on current state
            if (ShouldExitToMenUI())
            {
                if (enableDebugLogging)
                    Debug.Log("[LastUIInputBridge] Exit conditions met, requesting return to MenUI");

                RequestExitToMenUI();
            }
            else if (enableDebugLogging)
            {
                Debug.Log("[LastUIInputBridge] Exit conditions not met, staying in Last UI");
            }
        }

        private bool ShouldExitToMenUI()
        {
            if (_stateManager == null)
            {
                if (enableDebugLogging)
                    Debug.LogWarning("[LastUIInputBridge] StateManager not found, allowing exit");
                return true;
            }

            // If configured to exit only on main menu
            if (exitOnMainMenuOnly)
            {
                var activeCanvas = _stateManager.ActiveCanvas;
                if (activeCanvas != null && activeCanvas.canvasType != null)
                {
                    // Check if we're on the main menu canvas
                    bool isMainMenu = activeCanvas.canvasType.title.ToLower().Contains("main") ||
                                     activeCanvas.canvasType.title.ToLower().Contains("menu") ||
                                     activeCanvas == _stateManager.FirstCanvas;

                    if (enableDebugLogging)
                        Debug.Log($"[LastUIInputBridge] Current canvas: {activeCanvas.canvasType.title}, IsMainMenu: {isMainMenu}");

                    return isMainMenu;
                }
            }

            // Default: allow exit
            return true;
        }

        private void RequestExitToMenUI()
        {
            if (_sceneBridge != null)
            {
                if (enableDebugLogging)
                    Debug.Log("[LastUIInputBridge] Requesting MenUI to unload Last UI scene");

                _sceneBridge.OnLastUIExitRequested();
            }
            else
            {
                if (enableDebugLogging)
                    Debug.LogWarning("[LastUIInputBridge] Scene bridge not found, cannot request exit");
            }
        }

        /// <summary>
        /// Public method that can be called by UI buttons or other components
        /// </summary>
        public void ExitToMenUI()
        {
            if (enableDebugLogging)
                Debug.Log("[LastUIInputBridge] ExitToMenUI called directly");

            RequestExitToMenUI();
        }

        /// <summary>
        /// Check if scene bridge is available
        /// </summary>
        public bool IsSceneBridgeAvailable => _sceneBridge != null;

#if UNITY_EDITOR
        [ContextMenu("Test Exit to MenUI")]
        private void TestExitToMenUI()
        {
            if (Application.isPlaying)
            {
                ExitToMenUI();
            }
            else
            {
                Debug.LogWarning("[LastUIInputBridge] Test can only be run in Play Mode");
            }
        }
#endif
    }
}
