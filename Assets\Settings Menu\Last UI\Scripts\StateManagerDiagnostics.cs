using UnityEngine;
using UnityEngine.SceneManagement;

/// <summary>
/// Diagnostic script to help identify StateManager duplication issues
/// </summary>
public class StateManagerDiagnostics : MonoBehaviour
{
    [Header("Diagnostics")]
    [SerializeField] private bool enableDebugLogging = true;
    [SerializeField] private bool runDiagnosticsOnStart = true;

    private void Start()
    {
        if (runDiagnosticsOnStart)
        {
            RunDiagnostics();
        }
    }

    [ContextMenu("Run StateManager Diagnostics")]
    public void RunDiagnostics()
    {
        if (!enableDebugLogging) return;

        Debug.Log("=== StateManager Diagnostics ===");

        // Find all StateManager instances across all scenes
        var allStateManagers = FindObjectsOfType<StateManager>(true);
        Debug.Log($"Total StateManager instances found: {allStateManagers.Length}");

        for (int i = 0; i < allStateManagers.Length; i++)
        {
            var sm = allStateManagers[i];
            Debug.Log($"StateManager {i + 1}:");
            Debug.Log($"  - GameObject: {sm.gameObject.name}");
            Debug.Log($"  - Scene: {sm.gameObject.scene.name}");
            Debug.Log($"  - Active: {sm.gameObject.activeInHierarchy}");
            Debug.Log($"  - Enabled: {sm.enabled}");
            Debug.Log($"  - FirstCanvas: {(sm.FirstCanvas != null ? sm.FirstCanvas.name : "null")}");
            Debug.Log($"  - States Count: {GetStatesCount(sm)}");
        }

        // Check SceneServiceLocator registrations
        Debug.Log("\n=== SceneServiceLocator Diagnostics ===");
        
        // Check each loaded scene
        for (int i = 0; i < SceneManager.sceneCount; i++)
        {
            var scene = SceneManager.GetSceneAt(i);
            if (scene.isLoaded)
            {
                Debug.Log($"Scene: {scene.name}");
                
                var locator = FindSceneServiceLocator(scene);
                if (locator != null)
                {
                    Debug.Log($"  - SceneServiceLocator: Found");
                    var registeredSM = SceneServiceLocator.GetFromScene<StateManager>(scene);
                    Debug.Log($"  - Registered StateManager: {(registeredSM != null ? registeredSM.gameObject.name : "null")}");
                }
                else
                {
                    Debug.Log($"  - SceneServiceLocator: Not found");
                }
            }
        }

        // Check singleton instance
        Debug.Log($"\n=== Singleton Status ===");
        Debug.Log($"StateManager.Instance: {(StateManager.Instance != null ? StateManager.Instance.gameObject.name : "null")}");
        if (StateManager.Instance != null)
        {
            Debug.Log($"Instance Scene: {StateManager.Instance.gameObject.scene.name}");
        }

        Debug.Log("=== End Diagnostics ===");
    }

    private int GetStatesCount(StateManager sm)
    {
        try
        {
            // Use reflection to access private States field
            var statesField = typeof(StateManager).GetField("States", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            
            if (statesField != null)
            {
                var states = statesField.GetValue(sm) as System.Collections.Generic.List<GameObject>;
                return states?.Count ?? 0;
            }
        }
        catch (System.Exception e)
        {
            Debug.LogWarning($"Failed to get States count: {e.Message}");
        }
        
        return -1;
    }

    private SceneServiceLocator FindSceneServiceLocator(Scene scene)
    {
        var rootObjects = scene.GetRootGameObjects();
        foreach (var rootObj in rootObjects)
        {
            var locator = rootObj.GetComponentInChildren<SceneServiceLocator>();
            if (locator != null)
            {
                return locator;
            }
        }
        return null;
    }

    [ContextMenu("Force Clean Duplicate StateManagers")]
    public void ForceCleanDuplicates()
    {
        var allStateManagers = FindObjectsOfType<StateManager>(true);
        
        if (allStateManagers.Length <= 1)
        {
            Debug.Log("[StateManagerDiagnostics] No duplicates found");
            return;
        }

        Debug.Log($"[StateManagerDiagnostics] Found {allStateManagers.Length} StateManager instances. Cleaning duplicates...");

        // Keep the one with the most States configured
        StateManager bestStateManager = null;
        int maxStates = -1;

        foreach (var sm in allStateManagers)
        {
            int statesCount = GetStatesCount(sm);
            if (statesCount > maxStates)
            {
                maxStates = statesCount;
                bestStateManager = sm;
            }
        }

        // Destroy all others
        foreach (var sm in allStateManagers)
        {
            if (sm != bestStateManager)
            {
                Debug.Log($"[StateManagerDiagnostics] Destroying duplicate StateManager in {sm.gameObject.scene.name}");
                DestroyImmediate(sm.gameObject);
            }
        }

        Debug.Log($"[StateManagerDiagnostics] Kept StateManager in {bestStateManager.gameObject.scene.name} with {maxStates} states");
    }
}
