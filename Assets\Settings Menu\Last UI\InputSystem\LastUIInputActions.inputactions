{"name": "LastUIInputActions", "maps": [{"name": "LastUI", "id": "2b5b2c7a-3547-44c4-862a-64b8468da96b", "actions": [{"name": "Navigate", "type": "<PERSON><PERSON>", "id": "1ea41e7e-5a7e-4b06-bf35-b2215268e309", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Submit", "type": "<PERSON><PERSON>", "id": "12359d8e-ec39-4b7c-a429-efcd9ec7e399", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Cancel", "type": "<PERSON><PERSON>", "id": "2979ecda-f747-47ae-b8af-fabad69f84b6", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Point", "type": "PassThrough", "id": "5e654820-72ea-48b3-9557-03d9c548884b", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Click", "type": "PassThrough", "id": "1a5b464f-4633-4853-8c2f-106f6c6e912b", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "ScrollWheel", "type": "PassThrough", "id": "94d45d66-c368-426e-941e-86540829c0e3", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "MiddleClick", "type": "PassThrough", "id": "1d9e1fdd-287e-4c8d-b4e0-998dca72dfd1", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "RightClick", "type": "<PERSON><PERSON>", "id": "bdd8ae10-8e67-4cd6-bfcb-b28f3e7c70c0", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "TrackedDevicePosition", "type": "PassThrough", "id": "91033207-c286-4dd0-945c-1491a357bf6b", "expectedControlType": "Vector3", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "TrackedDeviceOrientation", "type": "PassThrough", "id": "e8abdb37-ca74-4b16-8a92-9e7e1688192b", "expectedControlType": "Quaternion", "processors": "", "interactions": "", "initialStateCheck": false}], "bindings": [{"name": "Gamepad", "id": "bf27f3ba-3d64-40f8-8d8a-2e4d3996d8d5", "path": "2DVector", "interactions": "", "processors": "", "groups": "", "action": "Navigate", "isComposite": true, "isPartOfComposite": false}, {"name": "up", "id": "3860e72a-d66c-4b3d-a1c7-fab93e6282c0", "path": "<Gamepad>/leftStick/up", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "up", "id": "a439eeda-7bd1-41c2-85c6-a88afea4b171", "path": "<Gamepad>/rightStick/up", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "401da86f-482d-4cd9-a0e1-0b262e75e844", "path": "<Gamepad>/leftStick/down", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "cea999e3-74da-42c1-89c1-2c2537859f09", "path": "<Gamepad>/rightStick/down", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "a98d82ae-dafe-4cb7-93ea-0a7ed1368266", "path": "<Gamepad>/leftStick/left", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "edc97b94-6602-49bb-913e-e5f346c268ed", "path": "<Gamepad>/rightStick/left", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "77bcd6e9-4c1f-4c96-bf7b-af741f3dbf8d", "path": "<Gamepad>/leftStick/right", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "f52b0765-fb06-418e-bb8f-fda476c38ba0", "path": "<Gamepad>/rightStick/right", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "", "id": "bcdb5309-62b5-4baf-8ea5-3a4b61ac74a8", "path": "<Gamepad>/dpad", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": false}, {"name": "Joystick", "id": "3f1d9361-9ba6-45b0-9ccc-fb93979a7240", "path": "2DVector", "interactions": "", "processors": "", "groups": "", "action": "Navigate", "isComposite": true, "isPartOfComposite": false}, {"name": "up", "id": "f8be90cd-734f-479e-b0b1-a7ce6215d742", "path": "<Joystick>/stick/up", "interactions": "", "processors": "", "groups": "Joystick", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "9b9d15bf-fde8-4cbb-9fc1-28620ed66723", "path": "<Joystick>/stick/down", "interactions": "", "processors": "", "groups": "Joystick", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "1632d5ee-8e0b-4bcf-b5bf-7ab0f34ec664", "path": "<Joystick>/stick/left", "interactions": "", "processors": "", "groups": "Joystick", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "660d7e9a-402a-4862-9b12-278853529326", "path": "<Joystick>/stick/right", "interactions": "", "processors": "", "groups": "Joystick", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "Keyboard", "id": "54acf08e-a1d8-493c-b984-33aa2644e276", "path": "2DVector", "interactions": "", "processors": "", "groups": "", "action": "Navigate", "isComposite": true, "isPartOfComposite": false}, {"name": "up", "id": "930afb2a-e275-468c-a9d1-8c2c57bb4ab0", "path": "<Keyboard>/w", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "up", "id": "1246d41c-62c7-41ef-beff-978092d8be82", "path": "<Keyboard>/upArrow", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "de48dfe0-a79d-4757-8142-e330819d013f", "path": "<Keyboard>/s", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "834d3d77-3b96-45e8-9263-221d7a4bffd9", "path": "<Keyboard>/downArrow", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "1dc2e65a-1dc3-4b3d-b50a-e4fea8f9eb72", "path": "<Keyboard>/a", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "e7af2885-b424-4d4e-b375-9b23ad2605c7", "path": "<Keyboard>/leftArrow", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "c54d6058-8337-4567-9bec-cea07bd38699", "path": "<Keyboard>/d", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "a0802d25-1f11-40ff-8017-cc8f23e482ac", "path": "<Keyboard>/rightArrow", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "", "id": "ca6533d7-0f6c-44cd-b614-02ad8bd91b9b", "path": "*/{Submit}", "interactions": "", "processors": "", "groups": "", "action": "Submit", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "submit-enter-key", "path": "<Keyboard>/enter", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Submit", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "submit-space-key", "path": "<Keyboard>/space", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Submit", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "submit-gamepad-south", "path": "<Gamepad>/buttonSouth", "interactions": "", "processors": "", "groups": "Gamepad", "action": "Submit", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "bacfa6af-cf2f-43dd-934c-787c11f8437c", "path": "*/{Cancel}", "interactions": "", "processors": "", "groups": "", "action": "Cancel", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "1be08fb5-6fa7-4fdc-ae20-df8d293b6959", "path": "<Mouse>/position", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Point", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "5831c0f7-99f9-4280-a753-d7677ac1bb09", "path": "<Pen>/position", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Point", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "77b08f30-bd27-46f4-9889-4feb55accaf1", "path": "<Touchscreen>/touch*/position", "interactions": "", "processors": "", "groups": "Touch", "action": "Point", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "428413af-d814-4964-a489-8830c4388dac", "path": "<Mouse>/leftButton", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "Click", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "69f9c494-e746-424a-9f99-f64b0fadf0d3", "path": "<Pen>/tip", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "Click", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "2a9fa05e-49b8-474a-a6c5-cea2a01c6add", "path": "<Touchscreen>/touch*/press", "interactions": "", "processors": "", "groups": "Touch", "action": "Click", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "55954592-5773-4c25-aa98-dab5ec5d13aa", "path": "<XRController>/trigger", "interactions": "", "processors": "", "groups": "XR", "action": "Click", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "23f2edd6-ae92-4ae5-ac77-cefd0168c01f", "path": "<Mouse>/scroll", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "ScrollWheel", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "e18fbf84-a660-437c-a359-620e4c667145", "path": "<Mouse>/middleButton", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "MiddleClick", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "985f6d2b-2014-4201-99d8-5ff054c58b6a", "path": "<Mouse>/rightButton", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "RightClick", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "3d485dd9-3db1-41f7-8ae8-a0be0c09baee", "path": "<XRController>/devicePosition", "interactions": "", "processors": "", "groups": "XR", "action": "TrackedDevicePosition", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "0b3072c8-4a69-4ddc-813d-34494fd34eaf", "path": "<XRController>/deviceRotation", "interactions": "", "processors": "", "groups": "XR", "action": "TrackedDeviceOrientation", "isComposite": false, "isPartOfComposite": false}]}], "controlSchemes": []}