using UnityEngine;

/// <summary>
/// Automatically removes StateManager components from non-Last UI scenes to prevent duplication
/// Add this script to any GameObject in your main scene to automatically clean up StateManager conflicts
/// </summary>
public class StateManagerSceneFilter : MonoBehaviour
{
    [Header("Settings")]
    [SerializeField] private bool enableDebugLogging = true;
    [SerializeField] private bool autoCleanupOnAwake = true;

    private void Awake()
    {
        if (autoCleanupOnAwake)
        {
            CleanupStateManagersInMainScene();
        }
    }

    [ContextMenu("Cleanup StateManagers in Main Scene")]
    public void CleanupStateManagersInMainScene()
    {
        if (enableDebugLogging)
            Debug.Log($"[StateManagerSceneFilter] Checking for StateManagers to cleanup in scene: {gameObject.scene.name}");

        // Check if this is a main scene (not a Last UI scene)
        if (IsMainScene())
        {
            // Find all StateManagers in this scene
            var stateManagers = FindObjectsOfType<StateManager>(true);
            
            foreach (var sm in stateManagers)
            {
                // Only remove StateManagers that are in the same scene as this filter
                if (sm.gameObject.scene == gameObject.scene)
                {
                    if (enableDebugLogging)
                        Debug.Log($"[StateManagerSceneFilter] Removing StateManager from main scene: {sm.gameObject.name}");
                    
                    DestroyImmediate(sm.gameObject);
                }
            }
        }
        else if (enableDebugLogging)
        {
            Debug.Log($"[StateManagerSceneFilter] Scene '{gameObject.scene.name}' appears to be a Last UI scene, keeping StateManagers");
        }
    }

    private bool IsMainScene()
    {
        string sceneName = gameObject.scene.name.ToLower();
        
        // Check for main scene indicators
        bool isMain = sceneName.Contains("ouroboros") || 
                     sceneName.Contains("base") || 
                     sceneName.Contains("main") ||
                     sceneName.Contains("level");

        // Check for Last UI scene indicators (if it has these, it's NOT a main scene)
        bool isLastUI = sceneName.Contains("settings") || 
                       sceneName.Contains("lastui") || 
                       sceneName.Contains("async") ||
                       sceneName.Contains("demo");

        if (enableDebugLogging)
            Debug.Log($"[StateManagerSceneFilter] Scene analysis: '{sceneName}' - IsMain: {isMain}, IsLastUI: {isLastUI}");

        return isMain && !isLastUI;
    }

    [ContextMenu("List All StateManagers")]
    public void ListAllStateManagers()
    {
        var allStateManagers = FindObjectsOfType<StateManager>(true);
        
        Debug.Log($"=== All StateManager Instances ({allStateManagers.Length}) ===");
        
        for (int i = 0; i < allStateManagers.Length; i++)
        {
            var sm = allStateManagers[i];
            Debug.Log($"{i + 1}. {sm.gameObject.name} in scene '{sm.gameObject.scene.name}'");
            Debug.Log($"   - Active: {sm.gameObject.activeInHierarchy}");
            Debug.Log($"   - FirstCanvas: {(sm.FirstCanvas != null ? sm.FirstCanvas.title + " (" + sm.FirstCanvas.name + ")" : "null")}");
        }
    }
}
