using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.SceneManagement;

[DefaultExecutionOrder(-50)] // Execute before other components that depend on StateManager
public class StateManager : MonoBehaviour
{
    [Tooltip("You need to add all of your states in here.")]
    [Header("List of States")]
    [SerializeField]
    List<GameObject> States = new List<GameObject>();


    [Tooltip("Assign starting state in here.")]
    public CanvasType FirstCanvas;

    [HideInInspector]
    public Animator CanvasAnimator;


    List<StateController> canvasControllerList;

    [HideInInspector]
    public StateController ActiveCanvas;
    [HideInInspector]
    public StateController PreviousCanvas;

    private InspectManager inspectManager;




    private void Awake()
    {
        Debug.Log("[StateManager] Awake called");

        // Ensure SceneServiceLocator exists first
        EnsureSceneServiceLocator();

        // Register with scene service locator
        SceneServiceLocator.Register(this);

        InitializeStateManager();





    }

    private void EnsureSceneServiceLocator()
    {
        var locator = FindObjectOfType<SceneServiceLocator>();
        if (locator == null)
        {
            Debug.Log("[StateManager] Creating SceneServiceLocator");
            var locatorGO = new GameObject("SceneServiceLocator");
            locatorGO.AddComponent<SceneServiceLocator>();
        }
    }

    private void InitializeStateManager()
    {
        Debug.Log("[StateManager] InitializeStateManager called");

        // Initialize CanvasAnimator if not already assigned
        if (CanvasAnimator == null)
        {
            CanvasAnimator = GetComponent<Animator>();
            if (CanvasAnimator == null)
            {
                Debug.LogWarning("[StateManager] No Animator component found. Canvas animations will not work.");
            }
            else
            {
                Debug.Log("[StateManager] CanvasAnimator successfully assigned");
            }
        }

        foreach (GameObject states in States)
        {
            if (states != null)
            {
                states.SetActive(true);
            }
            else
            {
                Debug.LogWarning("[StateManager] Null state found in States list. Please remove null entries from the States list in the inspector.");
            }
        }

        inspectManager = FindObjectOfType<InspectManager>();

        canvasControllerList = GetComponentsInChildren<StateController>().ToList();
        canvasControllerList.ForEach(x => x.gameObject.SetActive(false));
        StartCoroutine(PlayNextCanvasAnimation(FirstCanvas));
    }

    private void OnEnable()
    {
        StartCoroutine(PlayNextCanvasAnimation(FirstCanvas));
    }




    public void GoToNextCanvas(CanvasType _type)
    {
        if (ActiveCanvas != null)
        {
            ActiveCanvas.gameObject.SetActive(false);
        }

        inspectManager.DeactiveInspector();



        StateController NextCanvas = canvasControllerList.Find(x => x.canvasType == _type);
        if (NextCanvas != null)
        {

            PreviousCanvas = ActiveCanvas;
            NextCanvas.gameObject.SetActive(true);
            ActiveCanvas = NextCanvas;
            NextCanvas.GetComponent<StateController>().StartSelectable.Select();
        }
        else { Debug.LogWarning("The next canvas was not found!"); }


    }

    public void GoToPreviousCanvas()
    {



        if (ActiveCanvas != null)
        {
            ActiveCanvas.gameObject.SetActive(false);
        }

        inspectManager.DeactiveInspector();

        StateController NextCanvas = canvasControllerList.Find(x => x.canvasType == ActiveCanvas.previousCanvas);

        Debug.Log(NextCanvas);

        if (ActiveCanvas.canvasType.canGoPreviousCanvas == true)
        {
            PreviousCanvas = ActiveCanvas;
            NextCanvas.gameObject.SetActive(true);
            ActiveCanvas = NextCanvas;
            NextCanvas.GetComponent<StateController>().StartSelectable.Select();

            //Debug.Log("Can go previous canvas.");

        }
        else
        {
            //Debug.Log("Can't go previous canvas.");
        }
        //Debug.Log("Go Back Performed");
    }



    public IEnumerator PlayNextCanvasAnimation(CanvasType _type)
    {
        // Ensure CanvasAnimator is available
        if (CanvasAnimator == null)
        {
            Debug.LogWarning("[StateManager] CanvasAnimator is null. Skipping animation and going directly to canvas.");
            GoToNextCanvas(_type);
            yield break;
        }

        CanvasAnimator.Play("out_canvas");
        yield return new WaitForSeconds(0.1f);
        GoToNextCanvas(_type);

        CanvasAnimator.Play("in_canvas");
    }

    public IEnumerator PlayPreviousCanvasAnimation()
    {
        // Ensure CanvasAnimator is available
        if (CanvasAnimator == null)
        {
            Debug.LogWarning("[StateManager] CanvasAnimator is null. Skipping animation and going directly to previous canvas.");
            GoToPreviousCanvas();
            yield break;
        }

        CanvasAnimator.Play("out_canvas");
        yield return new WaitForSeconds(0.1f);
        GoToPreviousCanvas();
        CanvasAnimator.Play("in_canvas");
    }

    public void LeaveGame()
    {
        Application.Quit();
        Debug.Log("When you build, your game will close when submit this button.");
    }






}
