using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using System.Linq;

#if UNITY_EDITOR
using UnityEditor;
#endif

/// <summary>
/// Helper script to configure Last UI scene properly.
/// Fixes common issues like null States entries and missing FirstSelectedButton assignments.
/// </summary>
public class LastUISceneConfigHelper : MonoBehaviour
{
    [Header("Configuration")]
    [SerializeField] private bool enableDebugLogging = true;
    [SerializeField] private bool autoFixOnAwake = true;

    private void Awake()
    {
        if (autoFixOnAwake)
        {
            FixSceneConfiguration();
        }
    }

    [ContextMenu("Fix Scene Configuration")]
    public void FixSceneConfiguration()
    {
        if (enableDebugLogging)
            Debug.Log("[LastUISceneConfigHelper] Starting scene configuration fix...");

        FixStateManagerStates();
        FixUIInputManagerFirstButton();
        EnsureEventSystemActive();

        if (enableDebugLogging)
            Debug.Log("[LastUISceneConfigHelper] Scene configuration fix complete");
    }

    private void FixStateManagerStates()
    {
        var stateManager = FindObjectOfType<StateManager>();
        if (stateManager == null)
        {
            if (enableDebugLogging)
                Debug.LogWarning("[LastUISceneConfigHelper] No StateManager found in scene");
            return;
        }

        // Use reflection to access the private States field
        var statesField = typeof(StateManager).GetField("States", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        
        if (statesField != null)
        {
            var states = statesField.GetValue(stateManager) as System.Collections.Generic.List<GameObject>;
            if (states != null)
            {
                int nullCount = states.Count(s => s == null);
                if (nullCount > 0)
                {
                    // Remove null entries
                    states.RemoveAll(s => s == null);
                    
                    if (enableDebugLogging)
                        Debug.Log($"[LastUISceneConfigHelper] Removed {nullCount} null entries from StateManager.States");

#if UNITY_EDITOR
                    // Mark the StateManager as dirty so changes are saved
                    EditorUtility.SetDirty(stateManager);
#endif
                }
                else if (enableDebugLogging)
                {
                    Debug.Log("[LastUISceneConfigHelper] StateManager.States has no null entries");
                }
            }
        }
    }

    private void FixUIInputManagerFirstButton()
    {
        var uiInputManager = FindObjectOfType<UIInputManager>();
        if (uiInputManager == null)
        {
            if (enableDebugLogging)
                Debug.LogWarning("[LastUISceneConfigHelper] No UIInputManager found in scene");
            return;
        }

        // Use reflection to check FirstSelectedButton
        var firstButtonField = typeof(UIInputManager).GetField("FirstSelectedButton", 
            System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance);
        
        if (firstButtonField != null)
        {
            var firstButton = firstButtonField.GetValue(uiInputManager) as Button;
            if (firstButton == null)
            {
                // Try to find a suitable first button
                var stateManager = FindObjectOfType<StateManager>();
                if (stateManager != null && stateManager.FirstCanvas != null)
                {
                    // Find StateController with matching canvas type
                    var stateControllers = FindObjectsOfType<StateController>();
                    var targetController = stateControllers.FirstOrDefault(sc => sc.canvasType == stateManager.FirstCanvas);
                    
                    if (targetController != null && targetController.StartSelectable != null)
                    {
                        firstButtonField.SetValue(uiInputManager, targetController.StartSelectable);
                        
                        if (enableDebugLogging)
                            Debug.Log($"[LastUISceneConfigHelper] Set UIInputManager.FirstSelectedButton to: {targetController.StartSelectable.name}");

#if UNITY_EDITOR
                        EditorUtility.SetDirty(uiInputManager);
#endif
                    }
                    else if (enableDebugLogging)
                    {
                        Debug.LogWarning("[LastUISceneConfigHelper] Could not find suitable StartSelectable for FirstSelectedButton");
                    }
                }
            }
            else if (enableDebugLogging)
            {
                Debug.Log($"[LastUISceneConfigHelper] UIInputManager.FirstSelectedButton is already assigned: {firstButton.name}");
            }
        }
    }

    private void EnsureEventSystemActive()
    {
        var eventSystem = FindObjectOfType<EventSystem>();
        if (eventSystem != null)
        {
            bool wasInactive = !eventSystem.gameObject.activeInHierarchy || !eventSystem.enabled;
            
            if (!eventSystem.gameObject.activeInHierarchy)
            {
                eventSystem.gameObject.SetActive(true);
            }
            
            if (!eventSystem.enabled)
            {
                eventSystem.enabled = true;
            }

            if (wasInactive && enableDebugLogging)
            {
                Debug.Log("[LastUISceneConfigHelper] Activated EventSystem");
            }
        }
        else if (enableDebugLogging)
        {
            Debug.LogWarning("[LastUISceneConfigHelper] No EventSystem found in scene");
        }
    }

#if UNITY_EDITOR
    [ContextMenu("Validate Scene Setup")]
    private void ValidateSceneSetup()
    {
        Debug.Log("[LastUISceneConfigHelper] Scene validation:");
        
        // Check StateManager
        var stateManager = FindObjectOfType<StateManager>();
        Debug.Log($"  - StateManager: {(stateManager != null ? "✓" : "✗")}");
        
        if (stateManager != null)
        {
            var statesField = typeof(StateManager).GetField("States", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            if (statesField != null)
            {
                var states = statesField.GetValue(stateManager) as System.Collections.Generic.List<GameObject>;
                int nullCount = states?.Count(s => s == null) ?? 0;
                Debug.Log($"  - StateManager.States null entries: {nullCount}");
            }
            Debug.Log($"  - StateManager.FirstCanvas: {(stateManager.FirstCanvas != null ? "✓" : "✗")}");
        }
        
        // Check UIInputManager
        var uiInputManager = FindObjectOfType<UIInputManager>();
        Debug.Log($"  - UIInputManager: {(uiInputManager != null ? "✓" : "✗")}");
        
        if (uiInputManager != null)
        {
            var firstButtonField = typeof(UIInputManager).GetField("FirstSelectedButton", 
                System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance);
            var firstButton = firstButtonField?.GetValue(uiInputManager) as Button;
            Debug.Log($"  - UIInputManager.FirstSelectedButton: {(firstButton != null ? "✓" : "✗")}");
        }
        
        // Check EventSystem
        var eventSystem = FindObjectOfType<EventSystem>();
        Debug.Log($"  - EventSystem: {(eventSystem != null ? "✓" : "✗")}");
        if (eventSystem != null)
        {
            Debug.Log($"  - EventSystem active: {(eventSystem.gameObject.activeInHierarchy && eventSystem.enabled ? "✓" : "✗")}");
        }
    }
#endif
}
