using UnityEngine;
using UnityEngine.SceneManagement;
using UnityEngine.EventSystems;
using System.Collections.Generic;
using System.Linq;

/// <summary>
/// Comprehensive diagnostic tool for Last UI scene loading and display issues
/// </summary>
public class LastUISceneDiagnostics : MonoBehaviour
{
    [Header("Diagnostics Settings")]
    [SerializeField] private bool enableDebugLogging = true;
    [SerializeField] private bool runDiagnosticsOnStart = true;
    [SerializeField] private bool runDiagnosticsOnEnable = true;

    private void Start()
    {
        if (runDiagnosticsOnStart)
        {
            Invoke(nameof(RunComprehensiveDiagnostics), 0.5f); // Delay to let everything initialize
        }
    }

    private void OnEnable()
    {
        if (runDiagnosticsOnEnable)
        {
            Invoke(nameof(RunComprehensiveDiagnostics), 1f); // Delay to let everything initialize
        }
    }

    [ContextMenu("Run Last UI Scene Diagnostics")]
    public void RunComprehensiveDiagnostics()
    {
        if (!enableDebugLogging) return;

        Debug.Log("=== LAST UI SCENE DIAGNOSTICS ===");
        Debug.Log($"Current Scene: {gameObject.scene.name}");
        Debug.Log($"Diagnostics running on: {gameObject.name}");

        // 1. Scene and GameObject Analysis
        AnalyzeSceneStructure();

        // 2. StateManager Analysis
        AnalyzeStateManager();

        // 3. Canvas and UI Analysis
        AnalyzeCanvasStructure();

        // 4. Input System Analysis
        AnalyzeInputSystems();

        // 5. Service Locator Analysis
        AnalyzeServiceLocator();

        // 6. Canvas Type Configuration Analysis
        AnalyzeCanvasTypes();

        Debug.Log("=== END LAST UI DIAGNOSTICS ===");
    }

    private void AnalyzeSceneStructure()
    {
        Debug.Log("\n--- SCENE STRUCTURE ANALYSIS ---");
        
        var rootObjects = gameObject.scene.GetRootGameObjects();
        Debug.Log($"Root GameObjects in scene: {rootObjects.Length}");

        foreach (var rootObj in rootObjects)
        {
            Debug.Log($"  - {rootObj.name} (Active: {rootObj.activeInHierarchy})");
            
            // Check for important components
            var stateManager = rootObj.GetComponentInChildren<StateManager>();
            var uiInputManager = rootObj.GetComponentInChildren<UIInputManager>();
            var lastUIInputBridge = rootObj.GetComponentInChildren<LastUIInputBridge>();
            var sceneServiceLocator = rootObj.GetComponentInChildren<SceneServiceLocator>();

            if (stateManager) Debug.Log($"    └─ StateManager: {stateManager.gameObject.name}");
            if (uiInputManager) Debug.Log($"    └─ UIInputManager: {uiInputManager.gameObject.name}");
            if (lastUIInputBridge) Debug.Log($"    └─ LastUIInputBridge: {lastUIInputBridge.gameObject.name}");
            if (sceneServiceLocator) Debug.Log($"    └─ SceneServiceLocator: {sceneServiceLocator.gameObject.name}");
        }
    }

    private void AnalyzeStateManager()
    {
        Debug.Log("\n--- STATE MANAGER ANALYSIS ---");
        
        var stateManagers = FindObjectsOfType<StateManager>(true);
        Debug.Log($"StateManager instances found: {stateManagers.Length}");

        foreach (var sm in stateManagers)
        {
            Debug.Log($"StateManager: {sm.gameObject.name} in scene {sm.gameObject.scene.name}");
            Debug.Log($"  - Active: {sm.gameObject.activeInHierarchy}");
            Debug.Log($"  - Enabled: {sm.enabled}");
            
            // Use reflection to get States list
            var statesField = typeof(StateManager).GetField("States", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            
            if (statesField != null)
            {
                var states = statesField.GetValue(sm) as List<GameObject>;
                Debug.Log($"  - States count: {states?.Count ?? 0}");
                
                if (states != null)
                {
                    for (int i = 0; i < states.Count; i++)
                    {
                        var state = states[i];
                        if (state != null)
                        {
                            Debug.Log($"    {i + 1}. {state.name} (Active: {state.activeInHierarchy})");
                        }
                        else
                        {
                            Debug.Log($"    {i + 1}. NULL STATE");
                        }
                    }
                }
            }

            Debug.Log($"  - FirstCanvas: {(sm.FirstCanvas != null ? sm.FirstCanvas.name + " ('" + sm.FirstCanvas.title + "')" : "null")}");
            Debug.Log($"  - ActiveCanvas: {(sm.ActiveCanvas != null ? sm.ActiveCanvas.gameObject.name : "null")}");
            Debug.Log($"  - PreviousCanvas: {(sm.PreviousCanvas != null ? sm.PreviousCanvas.gameObject.name : "null")}");
        }
    }

    private void AnalyzeCanvasStructure()
    {
        Debug.Log("\n--- CANVAS STRUCTURE ANALYSIS ---");
        
        var canvases = FindObjectsOfType<Canvas>(true);
        Debug.Log($"Canvas components found: {canvases.Length}");

        foreach (var canvas in canvases)
        {
            Debug.Log($"Canvas: {canvas.gameObject.name}");
            Debug.Log($"  - Active: {canvas.gameObject.activeInHierarchy}");
            Debug.Log($"  - Enabled: {canvas.enabled}");
            Debug.Log($"  - Render Mode: {canvas.renderMode}");
            Debug.Log($"  - Sort Order: {canvas.sortingOrder}");
        }

        var stateControllers = FindObjectsOfType<StateController>(true);
        Debug.Log($"\nStateController components found: {stateControllers.Length}");

        foreach (var sc in stateControllers)
        {
            Debug.Log($"StateController: {sc.gameObject.name}");
            Debug.Log($"  - Active: {sc.gameObject.activeInHierarchy}");
            Debug.Log($"  - Enabled: {sc.enabled}");
            Debug.Log($"  - CanvasType: {(sc.canvasType != null ? sc.canvasType.name : "null")}");
            Debug.Log($"  - CanvasType Title: '{(sc.canvasType != null ? sc.canvasType.title : "null")}'");
            Debug.Log($"  - StartSelectable: {(sc.StartSelectable != null ? sc.StartSelectable.name : "null")}");
        }
    }

    private void AnalyzeInputSystems()
    {
        Debug.Log("\n--- INPUT SYSTEM ANALYSIS ---");
        
        var eventSystems = FindObjectsOfType<EventSystem>(true);
        Debug.Log($"EventSystem instances: {eventSystems.Length}");

        foreach (var es in eventSystems)
        {
            Debug.Log($"EventSystem: {es.gameObject.name}");
            Debug.Log($"  - Active: {es.gameObject.activeInHierarchy}");
            Debug.Log($"  - Enabled: {es.enabled}");
            Debug.Log($"  - Current Selected: {(es.currentSelectedGameObject != null ? es.currentSelectedGameObject.name : "null")}");
        }

        var uiInputManagers = FindObjectsOfType<UIInputManager>(true);
        Debug.Log($"\nUIInputManager instances: {uiInputManagers.Length}");

        foreach (var uim in uiInputManagers)
        {
            Debug.Log($"UIInputManager: {uim.gameObject.name}");
            Debug.Log($"  - Active: {uim.gameObject.activeInHierarchy}");
            Debug.Log($"  - Enabled: {uim.enabled}");
            Debug.Log($"  - FirstSelectedButton: {(uim.FirstSelectedButton != null ? uim.FirstSelectedButton.name : "null")}");
        }

        var lastUIInputBridges = FindObjectsOfType<LastUIInputBridge>(true);
        Debug.Log($"\nLastUIInputBridge instances: {lastUIInputBridges.Length}");

        foreach (var bridge in lastUIInputBridges)
        {
            Debug.Log($"LastUIInputBridge: {bridge.gameObject.name}");
            Debug.Log($"  - Active: {bridge.gameObject.activeInHierarchy}");
            Debug.Log($"  - Enabled: {bridge.enabled}");
        }
    }

    private void AnalyzeServiceLocator()
    {
        Debug.Log("\n--- SERVICE LOCATOR ANALYSIS ---");
        
        var locators = FindObjectsOfType<SceneServiceLocator>(true);
        Debug.Log($"SceneServiceLocator instances: {locators.Length}");

        foreach (var locator in locators)
        {
            Debug.Log($"SceneServiceLocator: {locator.gameObject.name} in scene {locator.gameObject.scene.name}");
            Debug.Log($"  - Active: {locator.gameObject.activeInHierarchy}");
            Debug.Log($"  - Enabled: {locator.enabled}");
        }

        // Test service retrieval
        var stateManagerFromService = SceneServiceLocator.Get<StateManager>();
        Debug.Log($"StateManager from service locator: {(stateManagerFromService != null ? stateManagerFromService.gameObject.name : "null")}");

        var uiInputManagerFromService = SceneServiceLocator.Get<UIInputManager>();
        Debug.Log($"UIInputManager from service locator: {(uiInputManagerFromService != null ? uiInputManagerFromService.gameObject.name : "null")}");
    }

    private void AnalyzeCanvasTypes()
    {
        Debug.Log("\n--- CANVAS TYPE ANALYSIS ---");
        
        var canvasTypes = Resources.FindObjectsOfTypeAll<CanvasType>();
        Debug.Log($"CanvasType assets found: {canvasTypes.Length}");

        foreach (var ct in canvasTypes)
        {
            Debug.Log($"CanvasType: {ct.name}");
            Debug.Log($"  - Title: '{ct.title}'");
            Debug.Log($"  - CanGoPreviousCanvas: {ct.canGoPreviousCanvas}");
        }
    }

    [ContextMenu("Force Activate First Canvas")]
    public void ForceActivateFirstCanvas()
    {
        var stateManager = FindObjectOfType<StateManager>();
        if (stateManager != null && stateManager.FirstCanvas != null)
        {
            Debug.Log($"[LastUISceneDiagnostics] Force activating first canvas: {stateManager.FirstCanvas.title}");
            stateManager.GoToNextCanvas(stateManager.FirstCanvas);
        }
        else
        {
            Debug.LogError("[LastUISceneDiagnostics] Cannot force activate - StateManager or FirstCanvas not found");
        }
    }

    [ContextMenu("List All Active UI Elements")]
    public void ListAllActiveUIElements()
    {
        Debug.Log("\n--- ACTIVE UI ELEMENTS ---");
        
        var selectables = FindObjectsOfType<UnityEngine.UI.Selectable>(true)
            .Where(s => s.gameObject.activeInHierarchy)
            .ToArray();

        Debug.Log($"Active selectable UI elements: {selectables.Length}");

        foreach (var selectable in selectables)
        {
            Debug.Log($"  - {selectable.name} ({selectable.GetType().Name}) - Interactable: {selectable.interactable}");
        }
    }
}
